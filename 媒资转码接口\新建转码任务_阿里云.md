# 新建转码任务

## 功能介绍

新建转码任务可以将视频进行转码，并在转码过程中压制水印、视频截图等。

## URI

POST /v1/{ProjectId}/transcodings

### 路径参数

* **参数:** ProjectId
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

## 请求参数

### 请求Header参数

* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

### 请求Body参数

* **参数:** Input
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 源文件存储地址，非追加音频等场景，该参数必选。

* **参数:** Output
    * **是否必选:** 是
    * **参数类型:** ResourceLocator object
    * **描述:** 输出文件存储地址。

* **参数:** TemplateId
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 转码模板ID。

* **参数:** Container
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 容器格式（封装格式），更多信息请参见下文Container详情。如设置则覆盖指定转码模板中的对应参数。

* **参数:** Video
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 视频转码参数。更多信息请参见下文Video详情。如设置则覆盖指定转码模板中的对应参数。

* **参数:** Audio
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 音频转码参数。更多信息请参见下文Audio详情。如设置则覆盖指定转码模板中的对应参数。

* **参数:** TransConfig
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 转码流程参数。更多信息请参见下文TransConfig详情。如设置则覆盖指定转码模板中的对应参数。
    示例：`{"TransMode":"onepass","AdjDarMethod":"none","IsCheckVideoBitrateFail":"true","IsCheckAudioBitrateFail":"true"}`。

* **参数:** MuxConfig
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 打包配置。更多信息请参见下文MuxConfig详情。如设置则覆盖指定转码模板中的对应参数。示例：`{"Segment":{"Duration":"10","ForceSegTime":"1,2,4,6,10,14,18"}}`，代表在第1、2、4、6、10、14、18、20、30、40、50...秒处强制分片。

* **参数:** UserData
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 用户自定义数据，该字段可在查询接口或消息通知中按原内容透传给用户。

* **参数:** WaterMarks
    * **是否必选:** 否
    * **参数类型:** Object[]
    * **描述:** 水印列表，将图片或文字压制到画面中。如设置则覆盖指定水印模板中的对应参数。更多信息请参见转码水印参数详情。

* **参数:** DeWatermark
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 模糊处理。更多信息请参见模糊处理详情。示例：`{"0": [{"l":10,"t":10,"w":10,"h":10},{"l":100,"t":0.1,"w":10,"h":10}],"128000": [],"250000": [{"l":0.2,"t":0.1,"w":0.01,"h":0.05}]}`。

* **参数:** Thumbnail
    * **是否必选:** 否
    * **参数类型:** Thumbnail object
    * **描述:** 截图信息。

* **参数:** Thumbnails
    * **是否必选:** 否
    * **参数类型:** Array of Thumbnail objects
    * **描述:** 多截图任务，数组，最多支持20个成员。

* **参数:** Clip
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 片段剪辑。更多信息请参见下文Clip详情。

* **参数:** Rotate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频顺时针旋转角度。值范围：0、90、180、270。默认值：0，代表不旋转。

* **参数:** Encryption
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 加密配置，仅当Container为m3u8时生效。更多信息请参见Encryption参数详情。示例：`{"Type":"hls-aes-128","Key":"ZW5jcnlwdGlvbmtleTEyMw","KeyType":"Base64","KeyUri":"aHR0cDovL2FsaXl1bi5jb20vZG9jdW1lbnQvaGxzMTI4LmtleQ=="}`。

* **参数:** Priority
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 任务优先级，取值如下：High（高优先级）、Normal（默认优先级）、Low（低优先级）。默认为Normal。
    当提交大量任务可能出现排队时，建议将时效性、重要内容设置为高优先级。



---

#### ResourceLocator

`ResourceLocator` 是一个统一的、多态的资源描述对象，用于精确指定文件在任何存储系统中的位置。

* **参数:** Type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源定位符的类型。它决定了 `Config` 对象的结构。可选值：LOCAL_FILE（本地文件系统）、MEDIA_ASSET（内部媒体资源ID）、OBJECT_STORAGE（云存储）、URL（外部网络地址）。

* **参数:** Config
    * **是否必选:** 是
    * **参数类型:** Object
    * **描述:** 定位符的具体配置信息，其结构由 `Type` 的值决定。

##### Config 结构详情

###### 当 Type = LOCAL_FILE

* **参数:** Path
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 文件系统路径。用于 Input 时：必须是单个文件的绝对路径。用于 Output 时：必须是目录的绝对路径，转码结果将存入此目录。

###### 当 Type = MEDIA_ASSET

* **参数:** MediaAssetId
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 在媒体资源库中注册的唯一ID。

###### 当 Type = OBJECT_STORAGE

* **参数:** StorageName
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 您在"对象存储配置接口"中创建的、用于标识特定存储账户的唯一自定义别名。例如: "r2-main-storage", "s3-main-storage"。

* **参数:** Bucket
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** Bucket（桶）的名称。

* **参数:** Key
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 对象的键（Key）。用于 Input 时：必须是单个对象的完整Key。用于 Output 时：必须是目录前缀。

* **参数:** Location
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 存储桶所在的地理区域。

###### 当 Type = URL

* **参数:** Url
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源的完整URL地址。

* **参数:** Headers
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 访问该URL时需要附加的HTTP请求头。

---


#### Thumbnail

* **参数:** Tar
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 是否压缩抽帧图片生成tar包。0：压缩, 1：不压缩。

* **参数:** Out
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 截图输出路径。

* **参数:** Params
    * **是否必选:** 是
    * **参数类型:** ThumbnailPara object
    * **描述:** 截图参数。

---

#### ThumbnailPara

* **参数:** Type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 采样类型。TIME, DOTS, DOTS_MS。默认：TIME。

* **参数:** Amount
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 黑点比例大于等于此值认为是黑帧。

* **参数:** Threshold
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 像素值小于此值认为是黑点。

* **参数:** Time
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样间隔 (秒)。取值：[1, 100]，默认12。

* **参数:** StartTime
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样开始时间 (秒)。默认：0。

* **参数:** Duration
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样持续时间 (秒或"ToEND")。默认：ToEND。

* **参数:** Dots
    * **是否必选:** 否
    * **参数类型:** Array of integers
    * **描述:** 指定时间点截图 (秒)。异步最多10个，同步1个。

* **参数:** DotsMs
    * **是否必选:** 否
    * **参数类型:** Array<Integer>
    * **长度限制:** 1
    * **描述:** 同步截图指定时间点（毫秒）。仅支持 1 个值。

* **参数:** OutputFileName
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 截图输出文件名。

* **参数:** Format
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 截图文件格式。1：jpg, 2：png (仅同步截图支持)。

* **参数:** Width
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 图片宽度 (px)。[96,3840]或0(自适应)。

* **参数:** Height
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 图片高度 (px)。[96,2160]或0(自适应)。

* **参数:** MaxLength
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 截图最长边的尺寸 (px)。

---




#### Container
* **参数:** Format
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 容器格式（封装格式）。

      格式支持：更多格式和与Codec的兼容要求，请参见格式支持。

      视频：3gp、avi、flv、f4v、fmp4、mkv、mov、mp4、ts、mxf、webm、m3u8、hls-fmp4、mpd、cmaf-hls、cmaf-dash。

      音频：aac、m4a、mp2、mp3、mp4、ogg、flac、m3u8、hls-fmp4、mpd、cmaf-hls、cmaf-dash。

      动图：gif、webp。

      默认值：mp4。

---


#### TransConfig
* **参数:** TransMode
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频转码模式。仅Codec为H.264、H.265、AV1时生效，且不同模式必须搭配Video的Bitrate、Crf正确设置。详细说明请参见视频码率控制模式。  
      取值如下：  
      - CBR：固定码率模式。  
      - onepass：一般用于ABR。编码速度较twopass更快。  
      - twopass：一般用于VBR。编码速度较onepass更慢。  
      - fixCRF：Crf质量控制模式。  
      默认值：有Bitrate为onepass，没有Bitrate为fixCRF，Crf使用默认值。

* **参数:** AdjDarMethod
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 分辨率改写方式。当Width或Height都填写时才生效，可以和LongShortMode搭配使用。  
      可选值：rescale、crop、pad、none。  
      默认值：none。  
      示例：请参见如何设置分辨率。

* **参数:** IsCheckReso
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查视频分辨率。IsCheckReso和IsCheckResoFail只支持二选一，IsCheckResoFail优先级更高。  
      - true：检查。当输入视频分辨率（宽或高）小于输出设置时，按输入视频分辨率转码。  
      - false：不检查。  
      默认值：false。

* **参数:** IsCheckResoFail
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查视频分辨率。IsCheckReso和IsCheckResoFail只支持二选一，本参数优先级更高。  
      - true：检查。当输入视频分辨率（宽或高）小于输出设置时，返回转码失败。  
      - false：不检查。  
      默认值：false。

* **参数:** IsCheckVideoBitrate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查视频码率。IsCheckVideoBitrate和IsCheckVideoBitrateFail只支持二选一，IsCheckVideoBitrateFail优先级更高。  
      - true：检查。当输入视频码率小于输出设置时，按输入视频码率转码。  
      - false：不检查。  
      默认值：false。

* **参数:** IsCheckVideoBitrateFail
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查视频码率。IsCheckVideoBitrate和IsCheckVideoBitrateFail只支持二选一，本参数优先级更高。  
      - true：检查。当输入视频码率小于输出设置时，返回转码失败。  
      - false：不检查。  
      默认值：false。

* **参数:** IsCheckAudioBitrate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查音频码率。IsCheckAudioBitrate和IsCheckAudioBitrateFail只支持二选一，IsCheckAudioBitrateFail优先级更高。  
      - true：检查。当输入音频码率小于输出设置时，按输入音频码率转码。  
      - false：不检查。  
      默认值：  
        - 参数为空，并且codec和输入源不一样：false。  
        - 参数为空，并且codec和输入源一样：true。

* **参数:** IsCheckAudioBitrateFail
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否检查音频码率。IsCheckAudioBitrate和IsCheckAudioBitrateFail只支持二选一，本参数优先级更高。  
      - true：检查。当输入音频码率小于输出设置时，返回转码失败。  
      - false：不检查。  
      默认值：false。


---


#### 视频码率控制模式
请根据视频码率控制模式，设置对应的TransMode、Bitrate、vbv（Maxrate、Bufsize）、Crf参数。

* **CBR固定码率模式**
    * **TransMode设置:** CBR
    * **码率相关参数设置:** Bitrate=Maxrate=Bufsize。

* **ABR平均码率模式**
    * **TransMode设置:** onepass或不指定
    * **码率相关参数设置:**  
      - 需指定Bitrate。  
      - 可选指定Maxrate和Bufsize控制峰值码率波动范围。

* **VBR动态码率模式**
    * **TransMode设置:** twopass
    * **码率相关参数设置:** 需指定Bitrate、Maxrate和Bufsize参数。

* **Crf质量控制模式**
    * **TransMode设置:** fixCRF
    * **码率相关参数设置:**  
      - 需指定Crf，若不指定则按对应codec的默认Crf值生效。  
      - 可选指定Maxrate和Bufsize控制峰值码率波动范围。

* **不指定模式**
    * **TransMode设置:** 不指定
    * **码率相关参数设置:** 不能指定Bitrate，将按对应codec的默认Crf值生效。

---

#### Video
* **参数:** Remove
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否删除视频流。  
      - true：删除。则本节所有Video相关参数失效。  
      - false：保留。  
      默认值：false。

* **参数:** Codec
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频编码格式。  
      可选值：H.264、H.265、AV1、GIF、WEBP。更多格式和与Container的兼容要求，请参见格式支持。  
      默认值：H.264。

* **参数:** Width
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 宽度（或长边）。当LongShortMode为false或未设置时，代表宽度；当LongShortMode为true时，代表视频的长边。  
      单位：px。  
      值范围：[128,4096]，必须为偶数。  
      默认值：  
        - 当Width和Height都未设置：使用输入视频的原始宽度（或长边）。  
        - 当仅设置Height：按照输入视频的原始比例来计算。

* **参数:** Height
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 高度（或短边）。当LongShortMode为false或未设置时，代表高度；当LongShortMode为true时，代表短边。  
      单位：px。  
      值范围：[128,4096]，必须为偶数。  
      默认值：  
        - 当Width和Height都未设置：使用输入视频的原始高度（或短边）。  
        - 当仅设置Width：按照输入视频的原始比例计算。

* **参数:** LongShortMode
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否开启横竖屏自适应（长短边自适应）。当Width或Height至少填写一项时生效。  
      - true：开启  
      - false：关闭  
      默认值：false。  
      最佳实践：输入视频有横屏和竖屏混合时，建议开启此选项并结合分辨率参数等比缩放，避免变形。

* **参数:** Fps
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 帧率。  
      单位：fps。  
      值范围：(0,60]。  
      默认值：输入文件原始帧率，超过60时取60。  
      常见值：24、25、30。

* **参数:** MaxFps
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 最大帧率。

* **参数:** Gop
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 设置相邻两个I帧之间的时间或帧数间隔。  
      Gop值越大，压缩率越高但编码速度越低，且分片时长、seek响应时间更长。  
      时间范围：[1,100000]秒；帧数范围：[1,100000]帧。  
      默认值：10s。  
      最佳实践：流媒体播放建议设为[2,7]秒。

* **参数:** Bitrate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出文件视频平均码率。使用CBR、ABR、VBR模式必须设置Bitrate，且TransMode需匹配。  
      单位：Kbps。  
      值范围：-1、[10,50000]。-1表示使用输入视频原始码率。  
      最佳实践：  
        - CBR：TransMode=CBR，Bitrate=Maxrate=Bufsize。  
        - ABR：TransMode=onepass，需指定Bitrate，可选Maxrate和Bufsize。  
        - VBR：TransMode=twopass，需指定Maxrate（或BitrateBnd）和Bufsize。

* **参数:** BitrateBnd
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频平均码率范围，仅H.264生效。  
      示例：{"Max":"5000","Min":"1000"}。

* **参数:** Maxrate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频码率峰值。  
      单位：Kbps。  
      值范围：[10,50000]。

* **参数:** Bufsize
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 码率控制缓冲区大小，用于控制码率波动。  
      Bufsize越大，码率波动越大，视频质量越高。  
      单位：Kb。  
      值范围：[1000,128000]。  
      默认值：6000。

* **参数:** Crf
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 质量控制因子。使用Crf模式必须设置Crf，且TransMode=fixCRF。  
      Crf值越大，质量越低，压缩率越高。  
      值范围：[20,51]。  
      默认值：H.264=23，H.265=26，AV1=32。  
      最佳实践：取值[23,29]，每±6约等于码率翻倍/减半。

* **参数:** Qscale
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频质量控制值（VBR可用），仅H.264生效。  
      值范围：[0,51]。

* **参数:** Profile
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 编码级别，仅H.264生效。  
      可选值：baseline、main、high。  
      默认值：high。

* **参数:** Preset
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** H.264编码器预置模式，仅H.264生效。  
      可选值：veryfast、fast、medium、slow、slower。  
      默认值：medium。

* **参数:** ScanMode
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 扫描模式。  
      - 空：按原模式  
      - auto：自动去隔行  
      - progressive：顺序扫描  
      - interlaced：交叉扫描  
      默认值：空。

* **参数:** PixFmt
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频颜色格式。  
      原始：PixFmt=""。  
      可选：yuv420p、yuvj420p、yuv422p、yuvj422p、yuv444p、yuvj444p、yuv444p161e、pc、bt470bg、smpte170m等。GIF支持bgr8。

* **参数:** Crop
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频剪裁。支持自动去黑边（border）或自定义：{width}:{height}:{left}:{top}。

* **参数:** Pad
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频贴黑边。格式：{width}:{height}:{left}:{top}。

---

#### Audio
* **参数:** Remove
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 是否删除音频流。  
      - true：删除，则本节所有Audio相关参数失效。  
      - false：保留。  
      默认值：false。

* **参数:** Codec
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音频编解码格式。  
      可选值：AAC、AC3、EAC3、MP2、MP3、FLAC、OPUS、VORBIS、WMA-V1、WMA-V2、pcm_s16le。更多格式和与Container的兼容要求，请参见格式支持。  
      默认值：AAC。  
      说明：转码时将音频编码格式设置为AC3、EAC3，会将普通音频转码为杜比格式，配合阿里云播放器SDK可在杜比设备上开启杜比专属音效。杜比音效制作属于音视频增强服务，中国内地按3.5元/分钟计费，不额外收取音频转码费用。详见音视频增强费用。

* **参数:** Profile
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音频编码级别，仅Codec为AAC时生效。  
      可选值：aac_low、aac_he、aac_he_v2、aac_ld、aac_eld。更多信息请参见基本概念。  
      默认值：aac_low。

* **参数:** Bitrate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出文件的音频码率。  
      单位：Kbps。  
      值范围：[8,1000]。  
      默认值：128。  
      常见值：64、128、256。

* **参数:** Samplerate
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 采样率。  
      单位：Hz。  
      可选值：22050、32000、44100、48000、96000。  
      说明：不同封装和编码格式支持的采样率不同，详见采样率支持。例如：Codec为MP3时，所有封装格式不支持96000，且封装为FLV时仅支持22050、44100。  
      默认值：44100。

* **参数:** Channels
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 声道数。  
      可选值：0、1、2、4、5、6、8。  
      - Codec为MP3、OPUS时：支持0、1、2。  
      - Codec为AAC、FLAC时：支持0、1、2、4、5、6、8。  
      - Codec为VORBIS时：支持2。  
      - Format为mpd时：不支持8。  
      默认值：2。  
      若要保留原声道数，请设置为0。

* **参数:** Volume
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音量控制，详见Volume音量控制详情。仅支持输出一路音频流时设置，多音频流不支持。

---

#### Volume音量控制详情
* **参数:** Method
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音量调整方式。  
      - auto：自动调整  
      - dynamic：动态调整  
      - linear：线性调整  
      默认值：dynamic。

* **参数:** Level
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 相对输入音频的音量提升幅度，当Method为linear时生效。  
      单位：db。  
      值范围：不超过20db。  
      默认值：-20db。

* **参数:** IntegratedLoudnessTarget
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 目标音量，当Method为dynamic时生效。  
      单位：db。  
      值范围：[-70,-5]。  
      默认值：-6。

* **参数:** TruePeak
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 最大峰值，当Method为dynamic时生效。  
      单位：db。  
      值范围：[-9,0]。  
      默认值：-1。

* **参数:** LoudnessRangeTarget
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 相对目标音量的波动范围，当Method为dynamic时生效。  
      单位：db。  
      值范围：[1,20]。  
      默认值：8。

---





#### 转码水印参数详情
* **参数:** Type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印类型。  
      - Text：文字水印（需设置TextWaterMark）  
      - Image：图片水印（需设置图片水印相关参数）  
      默认值：Image。

* **参数:** TextWaterMark
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 文字水印配置。详见文字水印参数详情。  
      若Type为Text，此参数必填。  
      示例：{"Content":"5rWL6K+V5paH5a2X5rC05Y2w","FontName":"SimSun","FontSize":"16","Top":2,"Left":10}。

* **参数:** InputFile
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 图片水印文件（OSS存储路径）。  
      格式要求：PNG（.png）、APNG（.apng）、MOV（.mov）、GIF（.gif）。  

* **参数:** WaterMarkTemplateId
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印模板ID。  
      不设置则使用默认值：位置TopRight、Dx/Dy=0、宽度=输出宽×0.12、高度按宽等比、时间为全程。

* **参数:** ReferPos
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印位置。  
      可选值：TopRight、TopLeft、BottomRight、BottomLeft。

* **参数:** Dx
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印相对输出视频的水平偏移量。  
      - 整数型：px，[8,4096]  
      - 小数型：占输出宽比例(0,1)，支持4位小数。

* **参数:** Dy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印相对输出视频的垂直偏移量。  
      - 整数型：px，[8,4096]  
      - 小数型：占输出高比例(0,1)，支持4位小数。

* **参数:** Width
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印宽度。  
      - 整数型：px，[8,4096]  
      - 小数型：占输出宽比例(0,1)，支持4位小数。

* **参数:** Height
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印高度。  
      - 整数型：px，[8,4096]  
      - 小数型：占输出高比例(0,1)，支持4位小数。

* **参数:** Timeline
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印显示时间，详见Timeline参数详情。

---

  
#### 文字水印参数详情
* **参数:** Content
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 文字水印内容（Base64编码）。  
      示例：添加文字水印“测试文字水印”，Content值为“5rWL6K+V5paH5a2X5rC05Y2w”。  
      说明：特殊字符（如emoji、单引号）可能导致截断或失败，请先转义后传入。

* **参数:** FontName
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 字体。  
      可选值：见字体支持。  
      默认值：SimSun。

* **参数:** FontSize
    * **是否必选:** 否
    * **参数类型:** Int
    * **描述:** 字号。  
      值范围：(4,120)  
      默认值：16。

* **参数:** FontColor
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 字体颜色。  
      可选值：见FontColor的name。  
      默认值：black。

* **参数:** FontAlpha
    * **是否必选:** 否
    * **参数类型:** Float
    * **描述:** 字体透明度。  
      值范围：(0,1]  
      默认值：1.0。

* **参数:** BorderWidth
    * **是否必选:** 否
    * **参数类型:** Int
    * **描述:** 描边宽度(px)。  
      值范围：[0,4096]  
      默认值：0。

* **参数:** BorderColor
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 描边颜色。  
      可选值：见BorderColor的name。  
      默认值：Black。

* **参数:** Top
    * **是否必选:** 否
    * **参数类型:** Int
    * **描述:** 文本上边距(px)。  
      值范围：[0,4096]  
      默认值：0。

* **参数:** Left
    * **是否必选:** 否
    * **参数类型:** Int
    * **描述:** 文本左边距(px)。  
      值范围：[0,4096]  
      默认值：0。

---

  
#### Timeline参数详情
本参数被WaterMarks.Timeline引用。

* **参数:** Start
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印开始出现时间。  
      格式：sssss[.SSS]  
      值范围：[0.000,86399.999]（超过片长会导致转码失败）  
      默认值：0  
      示例：18000.30。

* **参数:** Duration
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印持续时间。  
      - ToEND：持续显示到片尾  
      - {持续时长}：格式sssss[.SSS]，单位秒  
      默认值：ToEND。

---

#### 模糊处理详情

本参数被DeWatermark引用。

```json
{
//0ms开始，对距离视频画面左上角10×10像素处，大小为10×10像素的Logo，以及距离左边距100像素，上边距0.1×（src_height）的位置10×10的Logo进行模糊处理。
       "0": [
              {
                "l": 10,
                "t": 10,
                "w": 10,
                "h": 10
              },
              {
                "l": 100,
                "t": 0.1,
                "w": 10,
                "h": 10
              }
            ],
  //128000ms开始，停止对Logo进行模糊处理，即，[0~128000]ms为对Logo进行模糊处理的时间段。
     "128000": [],
  //250000ms开始，在左边距0.2×（src_width）、上边距0.1×（src_height）处，对大小为宽0.01×（src_width），高0.05×（src_height）的Logo进行模糊处理。
  "250000": [
              {
                "l": 0.2,
                "t": 0.1,
                "w": 0.01,
                "h": 0.05
              }
            ]
 }     
```
##### 字段说明

- **pts**：字符，指对象帧的时间戳。单位：毫秒（ms）。
- **l**：指模糊处理区域的左边距。
- **t**：指模糊处理区域的上边距。
- **w**：指模糊处理区域的宽度。
- **h**：指模糊处理区域的高度。

当l、t、w、h的值大于 1 时，表示绝对像素值。否则，表示相对片源分辨率的比例值。无论是比例值还是绝对值，都会在最终处理时进行截整处理。

---

#### Clip详情

本参数被Clip引用。

* **参数:** TimeSpan
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 剪辑时间区间，详见TimeSpan详情。  
      - 设置持续时长示例：{"Seek":"00:01:59.999","Duration":"180.30"}  
        表示从1分59秒999毫秒开始，截取到第5分30毫秒为止。  
      - 设置截尾时长示例：{"Seek":"00:01:59.999","End":"180.30"}  
        表示从1分59秒999毫秒开始，截取到距离片尾剩余5分30毫秒为止。

---

#### TimeSpan详情

本参数被Clip.TimeSpan引用。

* **参数:** Seek
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 开始时间点，可设置剪辑的起始位置，默认从开头截取。  
      格式：hh:mm:ss[.SSS] 或 sssss[.SSS]  
      值范围：[00:00:00.000,23:59:59.999] 或 [0.000,86399.999]  
      示例：00:01:59.999 或 180.30。

* **参数:** Duration
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 持续时长，相对于开始时间点（Seek）的剪辑时长。默认从Seek到视频结束。  
      Duration 与 End 只能二选一，设置 End 则 Duration 失效。  
      格式：hh:mm:ss[.SSS] 或 sssss[.SSS]  
      值范围：[00:00:00.000,23:59:59.999] 或 [0.000,86399.999]  
      示例：00:01:59.99 或 180.30。

* **参数:** End
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 截尾时长，表示切掉尾部的时长。  
      Duration 与 End 只能二选一，设置 End 则 Duration 失效。  
      格式：hh:mm:ss[.SSS] 或 sssss[.SSS]  
      值范围：[00:00:00.000,23:59:59.999] 或 [0.000,86399.999]  
      示例：00:01:59.999 或 180.30。

---



#### MuxConfig详情

本参数被MuxConfig引用。

* **参数:** Segment
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 分片配置字段，详见Segment详情。  
      仅当Container为m3u8、hls-fmp4、mpd、cmaf时生效。  
      示例：{"Duration":"10","ForceSegTime":"1,2,4,6,10,14,18"}  
      表示在第1、2、4、6、10、14、18、20、30、40、50...秒处强制分片。

---

#### Segment详情

本参数被MuxConfig.Segment引用。

* **参数:** Duration
    * **是否必选:** 否
    * **参数类型:** Int
    * **描述:** 分片时长。  
      单位：秒。  
      值范围：[1,60]  
      默认值：10，代表在第10、20、30、40秒处强制分片。

* **参数:** ForceSegTime
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 指定分片时间点列表（逗号分隔，最多10个）。  
      格式：{时间点},{时间点},{时间点}  
      时间点格式：小数型，最多3位小数  
      时间点单位：秒  
      示例：1,2,4,6,10,14,18 表示在第1、2、4、6、10、14、18秒处强制分片。

---

  
#### Encryption参数详情

本参数被Encryption引用。

* **参数:** Type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 视频的加密方式。  
      取值：  
      - hls-aes-128：标准加密。

* **参数:** KeyType
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 密钥的加密方式。  
      - Base64：基础加密方式  
      - KMS：密钥管理服务KMS方式（使用KMS生成明文密钥、密文密钥）

* **参数:** Key
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 加密视频的密文密钥（根据加密方式填写）。  
      - Base64：填写明文密钥经Base64加密生成的密文密钥。明文最长16位。  
        示例：明文 "encryptionkey128" → 密文 "ZW5jcnlwdGlvbmtleTEyOA=="。  
      - KMS：调用KMS的GenerateKMSDataKey，传入主密钥，KeySpec选AES_128，获取CiphertextBlob。  
        说明：主密钥由服务方提供。

* **参数:** KeyUri
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 解密服务地址（需您自建）。  
      KeyUri需Base64加密后传入，不能明文传输。  
      示例：URL `http://aliyun.com/document/hls128.key` → Base64 `"aHR0cDovL2FsaXl1bi5jb20vZG9jdW1lbnQvaGxzMTI4LmtleQ=="`。

* **参数:** SkipCnt
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 开头不加密的分片数量，用于视频快速起播。  
      示例：3。

---



## 响应参数

### 状态码：202

#### 响应Body参数

* **参数:** TaskId
    * **参数类型:** Integer
    * **描述:** 接受任务后，产生的任务ID。

### 状态码：403

#### 响应Body参数

* **参数:** ErrorCode
    * **参数类型:** String
    * **描述:** 错误码。

* **参数:** ErrorMsg
    * **参数类型:** String
    * **描述:** 错误描述。

## 状态码

* **状态码:** 202
    * **描述:** 新建转码任务提交成功。

* **状态码:** 403
    * **描述:** 新建转码任务提交失败。

## 错误码

请参见错误码定义文件。