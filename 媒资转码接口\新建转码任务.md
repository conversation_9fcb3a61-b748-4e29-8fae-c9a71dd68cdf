# 新建转码任务

## 功能介绍

新建转码任务可以将视频进行转码，并在转码过程中压制水印、视频截图等。视频转码前需要配置转码模板。

## URI

POST /v1/{project_id}/transcodings

### 路径参数

* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。


## 请求参数

### 请求Header参数
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

### 请求Body参数

* **参数:** input
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 源文件存储地址，非追加音频等场景，该参数必选。
* **参数:** output
    * **是否必选:** 是
    * **参数类型:** ResourceLocator object
    * **描述:** 输出文件存储地址。
* **参数:** trans_template_id
    * **是否必选:** 否
    * **参数类型:** Array of integers
    * **描述:** 转码模板ID，没带av_parameters参数时，必须带该参数，数组，每一路转码输出对应一个转码配置模板ID，最多支持9个模板ID。多个转码模板中如下参数可变，其他都必须一致：视频bitrate，height，width。
* **参数:** trans_template_list
    * **是否必选:** 否
    * **参数类型:** Array of TransIdTemplate objects
    * **描述:** 转码模板数组
* **参数:** av_parameters
    * **是否必选:** 否
    * **参数类型:** Array of AvParameters objects
    * **描述:** 转码参数。若同时设置“trans_template_id”和此参数，则优先使用此参数进行转码，不带trans_template_id时，该参数必选。
* **参数:** additional_manifests
    * **是否必选:** 否
    * **参数类型:** Array of AdditionalManifests objects
    * **描述:** 主索引定制参数。
* **参数:** output_filenames
    * **是否必选:** 否
    * **参数类型:** Array of strings
    * **描述:** 输出文件名称，每一路转码输出对应一个名称，需要与转码模板ID数组的顺序对应。若设置该参数，表示输出文件按该参数命名。若不设置该参数，表示输出文件按默认方式命名。
* **参数:** user_data
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 用户自定义数据，该字段可在查询接口或消息通知中按原内容透传给用户。
* **参数:** watermarks
    * **是否必选:** 否
    * **参数类型:** Array of WatermarkRequest objects
    * **描述:** 图片水印参数，数组，最多支持10个成员。
* **参数:** thumbnail
    * **是否必选:** 否
    * **参数类型:** Thumbnail object
    * **描述:** 截图信息。说明：仅“trans_template_id”对应的转码模板或“av_parameters”中设置的输出分辨率为1920×1080、1280×720、854×480或480×270时，视频转码才会输出对应的截图文件；否则将不输出截图文件。
* **参数:** thumbnails
    * **是否必选:** 否
    * **参数类型:** Array of Thumbnail objects
    * **描述:** 多截图任务，数组，最多支持20个成员。
* **参数:** image_sprites
    * **是否必选:** 否
    * **参数类型:** Array of ImageSprite objects
    * **描述:** 雪碧图参数，数组，最多支持20个成员。
* **参数:** priority
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 任务优先级，取值如下：high（高优先级）、normal（默认优先级）、low（低优先级）。默认为normal。
* **参数:** encryption
    * **是否必选:** 否
    * **参数类型:** Encryption object
    * **描述:** 视频加密控制参数。
* **参数:** crop
    * **是否必选:** 否
    * **参数类型:** Crop object
    * **描述:** 截取前多少秒做转码。
* **参数:** video_process
    * **是否必选:** 否
    * **参数类型:** VideoProcess object
    * **描述:** 视频处理控制参数
* **参数:** audio_process
    * **是否必选:** 否
    * **参数类型:** AudioProcess object
    * **描述:** 音频处理控制参数

---

**TransIdTemplate**
* **参数:** template_id
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 输出视频对应的模板ID
* **参数:** output
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 输出路径，不填写时与外层转码输出在一起。说明：hls多路输出时，每路输出路径不同时，index.m3u8将不可用，音视频分离不可用。
* **参数:** output_filename
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出文件名

---

**AvParameters**
* **参数:** video
    * **是否必选:** 否
    * **参数类型:** VideoParameters object
    * **描述:** 视频参数
* **参数:** audio
    * **是否必选:** 否
    * **参数类型:** Audio object
    * **描述:** 音频参数
* **参数:** common
    * **是否必选:** 是
    * **参数类型:** Common object
    * **描述:** 公共参数
* **参数:** output
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 输出路径，不填写时与外层转码输出在一起。说明：hls多路输出时，每路输出路径不同时，index.m3u8将不可用，音视频分离不可用。
* **参数:** output_filename
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出文件名

---

**VideoParameters**
* **参数:** output_policy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出策略。取值：discard, transcode, copy。
* **参数:** codec
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频编码格式。1：H264, 2：H265。
* **参数:** crf
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频恒定码率控制因子。取值范围：[0, 51]。
* **参数:** max_bitrate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 输出最大码率 (kbit/s)。
* **参数:** bitrate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 输出平均码率 (kbit/s)。取值：0或[40,50000]。0为自适应。
* **参数:** profile
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 编码档次。1：H264_BASE, 2：H264_MAIN, 3：H264_HIGH, 4：H265_MAIN。
* **参数:** level
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 编码级别。0：AUTO, 1-15 对应不同级别。
* **参数:** preset
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 编码质量等级。1：SPEED, 2：NORMAL, 3：HIGHQUALITY。默认1。
* **参数:** max_iframes_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** I帧最大间隔 (秒)。取值：[2, 10]，默认5。
* **参数:** bframes_count
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 最大B帧间隔 (帧)。H264:[0,8], H265:[0,7]。
* **参数:** frame_rate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 帧率 (帧/秒)。取值：0或[5,60]。0为自适应。
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频宽度 (像素)。H264:[32,4096], H265:[320,4096]，必须为2的倍数。
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频高度 (像素)。H264:[32,2880], H265:[240,2880]，必须为2的倍数。
* **参数:** black_cut
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 黑边剪裁类型。0：不开启, 1：低复杂度, 2：高复杂度。
* **参数:** stream_name
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 流名称。

---

**Audio**
* **参数:** output_policy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 输出策略。取值：discard, transcode, copy。
* **参数:** codec
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 音频编码格式。1：AAC, 2：HEAAC1, 3：HEAAC2, 4：MP3。
* **参数:** sample_rate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 音频采样率。1：AUTO, 2：22050Hz, 3：32000Hz, 4：44100Hz, 5：48000Hz, 6：96000Hz。
* **参数:** bitrate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 音频码率 (kbit/s)。取值：0或[8,1000]。
* **参数:** channels
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 声道数。1：单声道, 2：双声道, 6：5.1声道。

---

**Common**
* **参数:** PVC
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 是否开启高清低码功能。true：开启, false：关闭。
* **参数:** hls_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** HLS分片间隔 (秒)。取值：[2, 10]。
* **参数:** dash_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** DASH间隔 (秒)。取值：[2, 10]。
* **参数:** pack_type
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 封装格式。1:HLS, 2:DASH, 3:HLS+DASH, 4:MP4, 5:MP3, 6:ADTS, 8:MOV, 9:FLV, 10:AVI。

---

**AdditionalManifests**
* **参数:** manifest_name_modifier
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 定制的索引后缀名。
* **参数:** selected_outputs
    * **是否必选:** 否
    * **参数类型:** Array of strings
    * **描述:** 选择的流名称。

---

**WatermarkRequest**
* **参数:** input
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 源文件地址，图片水印时必选。
* **参数:** template_id
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印模板ID。
* **参数:** text_context
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 文字水印内容 (Base64编码)。
* **参数:** image_watermark
    * **是否必选:** 否
    * **参数类型:** ImageWatermark object
    * **描述:** 图片水印参数，覆盖模板。
* **参数:** text_watermark
    * **是否必选:** 否
    * **参数类型:** TextWatermark object
    * **描述:** 文字水印配置。

---

**ImageWatermark**
* **参数:** dx
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水平偏移量 (px或比率)。
* **参数:** dy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 垂直偏移量 (px或比率)。
* **参数:** referpos
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印位置。TopRight, TopLeft, BottomRight, BottomLeft, ClockWise, AntiClockWise, Random。
* **参数:** timeline_start
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印开始时间 (秒)。
* **参数:** timeline_duration
    * **是否必选:** 否
    * **参数类型:** Integer | String
    * **描述:** 水印持续时间 (秒或"ToEND")。
* **参数:** random_time_min
    * **是否必选:** 否
    * **参数类型:** Float
    * **描述:** 轮转间隔时间最小值 (秒)。
* **参数:** random_time_max
    * **是否必选:** 否
    * **参数类型:** Float
    * **描述:** 轮转间隔时间最大值 (秒)。
* **参数:** image_process
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片水印处理方式。Original, Grayed, Transparent。
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片宽 (px或比率)。
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片高 (px或比率)。
* **参数:** base
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印叠加母体。input, output。

---

**TextWatermark**
* **参数:** dx
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水平偏移量 (px或比率)。
* **参数:** dy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 垂直偏移量 (px或比率)。
* **参数:** referpos
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印位置。TopRight, TopLeft, BottomRight, BottomLeft, ClockWise, AntiClockWise, Random。
* **参数:** timeline_start
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印开始时间 (秒)。
* **参数:** timeline_duration
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印持续时间 (秒或"ToEND")。
* **参数:** random_time_min
    * **是否必选:** 否
    * **参数类型:** Float
    * **描述:** 轮转间隔时间最小值 (秒)。
* **参数:** random_time_max
    * **是否必选:** 否
    * **参数类型:** Float
    * **描述:** 轮转间隔时间最大值 (秒)。
* **参数:** font_name
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 字体，当前支持fzyouh。
* **参数:** font_size
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 字体大小。取值：[4, 120]。
* **参数:** font_color
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 字体颜色。支持：black, blue, white, green, red, yellow等。
* **参数:** base
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印叠加母体。input, output。默认：input。

---

**Thumbnail**
* **参数:** tar
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 是否压缩抽帧图片生成tar包。0：压缩, 1：不压缩。
* **参数:** out
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 截图输出路径。
* **参数:** params
    * **是否必选:** 是
    * **参数类型:** ThumbnailPara object
    * **描述:** 截图参数。

---

**ThumbnailPara**
* **参数:** type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 采样类型。TIME, DOTS, DOTS_MS。默认：TIME。
* **参数:** amount
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 黑点比例大于等于此值认为是黑帧。
* **参数:** threshold
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 像素值小于此值认为是黑点。
* **参数:** time
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样间隔 (秒)。取值：[1, 100]，默认12。
* **参数:** start_time
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样开始时间 (秒)。默认：0。
* **参数:** duration
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 采样持续时间 (秒或"ToEND")。默认：ToEND。
* **参数:** dots
    * **是否必选:** 否
    * **参数类型:** Array of integers
    * **描述:** 指定时间点截图 (秒)。异步最多10个，同步1个。
* **参数:** dots_ms
    * **是否必选:** 否
    * **参数类型:** Array<Integer>
    * **长度限制:** 1
    * **描述:** 同步截图指定时间点（毫秒）。仅支持 1 个值。
* **参数:** output_filename
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 截图输出文件名。
* **参数:** format
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 截图文件格式。1：jpg, 2：png (仅同步截图支持)。
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 图片宽度 (px)。[96,3840]或0(自适应)。
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 图片高度 (px)。[96,2160]或0(自适应)。
* **参数:** max_length
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 截图最长边的尺寸 (px)。

---

**ImageSprite**
* **参数:** params
    * **是否必选:** 是
    * **参数类型:** ImageSpritePara object
    * **描述:** 雪碧图参数。
* **参数:** output
    * **是否必选:** 否
    * **参数类型:** ResourceLocator object
    * **描述:** 雪碧图输出路径。
* **参数:** output_object_name
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 雪碧图图片文件的输出文件名。
* **参数:** webvtt_object_name
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** Web VTT文件的输出路径。

---

**ImageSpritePara**
* **参数:** sample_type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 采样类型。PERCENT, TIME。
* **参数:** sample_interval
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 采样间隔。PERCENT时为百分比(0,100], TIME时为秒(0,360000]。
* **参数:** row_count
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 雪碧图中小图的行数。行数*列数 <= 100。
* **参数:** column_count
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 雪碧图中小图的列数。行数*列数 <= 100。
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 雪碧图中小图宽度 (px)。0或[96, 4096]。
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 雪碧图中小图高度 (px)。0或[96, 4096]。
* **参数:** resolution_adaptive
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 分辨率自适应。open, close。
* **参数:** fill_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 填充方式。stretch, black, white。
* **参数:** format
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 图片格式。jpg, png。默认：jpg。

---

**Encryption**
* **参数:** hls_encrypt
    * **是否必选:** 否
    * **参数类型:** HlsEncrypt object
    * **描述:** 视频加密控制参数。

---

**HlsEncrypt**
* **参数:** key
    * **是否必选:** 是
    * **参数类型:** String (Base64 编码)
    * **描述:** 加密密钥，原始二进制数据经 Base64 编码后得到的字符串。
* **参数:** url
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 密钥获取服务的地址，用于播放hls分片时解密使用。
* **参数:** iv
    * **是否必选:** 否
    * **参数类型:** String (Base64 编码)
    * **描述:** 初始向量（Initialization Vector，IV），随机生成的二进制数据，经 Base64 编码后表示。
* **参数:** algorithm
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 加密算法。AES-128-CTR, AES-128-CBC。默认：AES-128-CTR。

---

**Crop**
* **参数:** duration
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 截取的视频时长 (秒)，从0秒开始。

---

**ResourceLocator**

`ResourceLocator` 是一个统一的、多态的资源描述对象，用于精确指定文件在任何存储系统中的位置。

* **参数:** type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源定位符的类型。它决定了 `config` 对象的结构。可选值：LOCAL_FILE（本地文件系统）、MEDIA_ASSET（内部媒体资源ID）、OBJECT_STORAGE（云存储）、URL（外部网络地址）。
* **参数:** config
    * **是否必选:** 是
    * **参数类型:** Object
    * **描述:** 定位符的具体配置信息，其结构由 `type` 的值决定。

### **config 结构详情**

#### 当 type = LOCAL_FILE
* **参数:** path
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 文件系统路径。用于 input 时：必须是单个文件的绝对路径。用于 output 时：必须是目录的绝对路径，转码结果将存入此目录。

#### 当 type = MEDIA_ASSET
* **参数:** mediaAssetId
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 在媒体资源库中注册的唯一ID。

#### 当 type = OBJECT_STORAGE
* **参数:** storageName
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 您在"对象存储配置接口"中创建的、用于标识特定存储账户的唯一自定义别名。例如: "r2-main-storage", "s3-main-storage"。系统会根据此名称查找对应的提供商类型（R2/S3）、访问凭证和终端节点（Endpoint）等信息。
* **参数:** bucket
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** Bucket（桶）的名称。
* **参数:** key
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 对象的键（Key）。用于 input 时：必须是单个对象的完整Key。用于 output 时：必须是目录前缀。
* **参数:** location
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 存储桶所在的地理区域。对于 Cloudflare R2，此项为可选，可省略或推荐使用 auto。对于 AWS S3 等强区域性服务，此项为必需，例如 us-east-1。

#### 当 type = URL
* **参数:** url
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源的完整URL地址。
* **参数:** headers
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 访问该URL时需要附加的HTTP请求头。可用于身份验证等场景。这是一个键值对对象，例如 {"Authorization": "Bearer your-token"}。

---

**VideoProcess**
* **参数:** hls_init_count
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** HLS起始分片数量。取值：[0, 10]。
* **参数:** hls_init_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 起始分片时长 (秒)。取值：[2, 10]。
* **参数:** hls_storage_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** HLS音视频流存储方式。composite, separate。
* **参数:** rotate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频顺时针旋转角度。0:不转, 1:90度, 2:180度, 3:270度。
* **参数:** adaptation
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 长短边自适应。SHORT, LONG, NONE。
* **参数:** fill_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 填充方式 (adaptation为NONE时)。stretch, black, white。
* **参数:** upsample
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 是否开启上采样。0：关闭, 1：开启。
* **参数:** hls_segment_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** HLS切片类型。mpegts, fmp4。默认：mpegts。

---

**AudioProcess**
* **参数:** volume
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音量调整方式。auto, dynamic, original。
* **参数:** volume_expr
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 音量调整幅值 (dB)，volume为dynamic时。取值：[-15, 15]。

---

## 响应参数
### 状态码：202
**响应Body参数**
* **参数:** task_id
    * **参数类型:** Integer
    * **描述:** 接受任务后，产生的任务ID。


### 状态码：403
**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。



## 状态码
* **状态码:** 202
    * **描述:** 新建转码任务提交成功。
* **状态码:** 403
    * **描述:** 新建转码任务提交失败。



## 错误码 
请参见错误码定义文件。