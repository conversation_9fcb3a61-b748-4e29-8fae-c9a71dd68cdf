# 取消视频解析任务

## 功能介绍

取消已下发的视频解析任务，仅支持取消排队中的任务。

## URI

DELETE /v1/{project_id}/extract-metadata

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

---

**Query参数**
* **参数:** task_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 任务ID

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

## 响应参数

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 状态码

****
* **状态码:** 204
    * **描述:** 取消视频解析任务成功。
* **状态码:** 400
    * **描述:** 取消视频解析任务失败。

## 错误码

请参见错误码。
