# 查询自定义模板组集合

## 功能介绍

查询转码模板组集合。

## URI

GET /v1/{project_id}/asset/template-collection/transcodings

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

---

**Query参数**
* **参数:** group_collection_id
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 模板组集合id
* **参数:** offset
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 偏移量。默认为0。指定group_collection_id时该参数无效。
* **参数:** limit
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 每页记录数。默认为10，范围[1,100]。指定group_collection_id时该参数无效。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

## 响应参数

### 状态码：200

**响应Body参数**
* **参数:** template_group_collection_list
    * **参数类型:** Array of TemplateGroupCollection objects
    * **描述:** 模板组集合信息
* **参数:** total
    * **参数类型:** Integer
    * **描述:** 总记录条数

**TemplateGroupCollection**
* **参数:** group_collection_id
    * **参数类型:** String
    * **描述:** 模板组集合id
* **参数:** name
    * **参数类型:** String
    * **描述:** 模板组集合名称
* **参数:** description
    * **参数类型:** String
    * **描述:** 模板介绍
* **参数:** template_group_list
    * **参数类型:** Array of TemplateGroup objects
    * **描述:** 转码组列表

**TemplateGroup**
* **参数:** group_id
    * **参数类型:** String
    * **描述:** 模板组id
* **参数:** name
    * **参数类型:** String
    * **描述:** 模板组名称
* **参数:** status
    * **参数类型:** String
    * **描述:** 是否默认
* **参数:** type
    * **参数类型:** String
    * **描述:** 模板组类型
* **参数:** auto_encrypt
    * **参数类型:** Integer
    * **描述:** 是否自动加密。 取值如下： 0：表示不加密。 1：表示需要加密。 默认值：0。 加密与转码必须要一起进行，当需要加密时，转码参数不能为空，且转码输出格式必须要为HLS。
* **参数:** quality_info_list
    * **参数类型:** Array of QualityInfo objects
    * **描述:** 画质配置信息列表
* **参数:** watermark_template_ids
    * **参数类型:** Array of strings
    * **描述:** 绑定的水印模板组ID数组
* **参数:** description
    * **参数类型:** String
    * **描述:** 模板介绍
* **参数:** common
    * **参数类型:** Common object
    * **描述:** 高清低码开关，高清低码版本，编码格式多路共同参数

**QualityInfo**
* **参数:** video
    * **参数类型:** VideoTemplateInfo object
    * **描述:** 模板视频信息
* **参数:** audio
    * **参数类型:** AudioTemplateInfo object
    * **描述:** 模板音频信息
* **参数:** format
    * **参数类型:** String
    * **描述:** 格式。

**VideoTemplateInfo**
* **参数:** quality
    * **参数类型:** String
    * **描述:** 画质。
* **参数:** width
    * **参数类型:** Integer
    * **描述:** 视频宽度。
* **参数:** height
    * **参数类型:** Integer
    * **描述:** 视频高度。
* **参数:** bitrate
    * **参数类型:** Integer
    * **描述:** 码率。
* **参数:** frame_rate
    * **参数类型:** Integer
    * **描述:** 帧率（默认为1，1代表自适应，单位是帧每秒）。

**AudioTemplateInfo**
* **参数:** sample_rate
    * **参数类型:** Integer
    * **描述:** 音频采样率（有效值范围） 1：AUDIO_SAMPLE_AUTO 2：AUDIO_SAMPLE_22050 3：AUDIO_SAMPLE_32000 4：AUDIO_SAMPLE_44100 5：AUDIO_SAMPLE_48000 6：AUDIO_SAMPLE_96000 默认值为1。
* **参数:** bitrate
    * **参数类型:** Integer
    * **描述:** 音频码率（单位：Kbps）。
* **参数:** channels
    * **参数类型:** Integer
    * **描述:** 声道数（有效值范围） 1：AUDIO_CHANNELS_1 2：AUDIO_CHANNELS_2

**Common**
* **参数:** pvc
    * **参数类型:** String
    * **描述:** 高清低码开关。
* **参数:** pvc_version
    * **参数类型:** String
    * **描述:** 高清低码版本。
* **参数:** video_codec
    * **参数类型:** String
    * **描述:** 视频编码格式。
* **参数:** audio_codec
    * **参数类型:** String
    * **描述:** 音频编码格式（有效值范围） 1：AUDIO_CODECTYPE_AAC 2：AUDIO_CODECTYPE_HEAAC1 3：AUDIO_CODECTYPE_HEAAC2 4：AUDIO_CODECTYPE_MP3 默认值为1。
* **参数:** hls_interval
    * **参数类型:** Integer
    * **描述:** 分片时长（默认为5秒）。

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

查询转码模板集合

```bash
GET https://{endpoint}/v1/{project_id}/asset/template-collection/transcodings
```

## 响应示例

* **状态码:** 200
* **描述:** 处理成功返回。

```json
{
  "template_group_collection_list" : [ {
    "group_collection_id" : "9751249d25f14587b212544d6fd8dcf8",
    "name" : "test",
    "template_group_list" : [ {
      "group_id" : "9751249d25f14587b212544d6fd8dcf8",
      "name" : "test112",
      "status" : "0",
      "type" : "custom_template_group",
      "auto_encrypt" : 0,
      "quality_info_list" : [ {
        "video" : {
          "quality" : "UNKNOW",
          "width" : 0,
          "height" : 0,
          "bitrate" : 0,
          "frame_rate" : 0
        },
        "audio" : null,
        "format" : "UNKNOW"
      } ],
      "watermark_template_ids" : null,
      "description" : null,
      "common" : {
        "pvc" : null,
        "pvc_version" : null,
        "video_codec" : null,
        "audio_codec" : "AAC",
        "hls_interval" : 0
      }
    } ]
  } ],
  "total" : 1
}
```

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 200
    * **描述:** 处理成功返回。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
