# 查询水印列表

## 功能介绍

查询水印模板

## URI

GET /v1/{project_id}/template/watermark

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

---

**Query参数**
* **参数:** id
    * **是否必选:** 否
    * **参数类型:** Array of strings
    * **描述:** 水印模板配置id，一次最多10个。
* **参数:** page
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 分页编号。 默认为0。指定id时该参数无效。
* **参数:** size
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 每页记录数。 默认为10，范围[1,100]。指定id时该参数无效。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

## 响应参数

### 状态码：200

**响应Body参数**
* **参数:** templates
    * **参数类型:** Array of WatermarkTemplate objects
    * **描述:** 水印模板信息。
* **参数:** total
    * **参数类型:** Integer
    * **描述:** 总数。

**WatermarkTemplate**
* **参数:** name
    * **参数类型:** String
    * **描述:** 水印模板名称。
* **参数:** id
    * **参数类型:** String
    * **描述:** 水印模板配置id。
* **参数:** status
    * **参数类型:** Long
    * **描述:** 启用状态。 取值为： 0：停用 1：启用
* **参数:** dx
    * **参数类型:** String
    * **描述:** 水印图片相对输出视频的水平偏移量。 默认值是0。
* **参数:** dy
    * **参数类型:** String
    * **描述:** 水印图片相对输出视频的垂直偏移量。 默认值是0。
* **参数:** position
    * **参数类型:** String
    * **描述:** 水印的位置。
* **参数:** width
    * **参数类型:** String
    * **描述:** 水印图片宽。
* **参数:** height
    * **参数类型:** String
    * **描述:** 水印图片高。
* **参数:** create_time
    * **参数类型:** String
    * **描述:** 创建时间。
* **参数:** image_url
    * **参数类型:** String
    * **描述:** 水印图片下载url。
* **参数:** type
    * **参数类型:** String
    * **描述:** 水印图片格式类型。
* **参数:** watermark_type
    * **参数类型:** String
    * **描述:** 水印类型，当前只支持IMAGE（图片水印）。
* **参数:** image_process
    * **参数类型:** String
    * **描述:** watermark_type设置为IMAGE时有效。 目前包括： ORIGINAL：只做简单缩放，不做其他处理 TRANSPARENT：图片底色透明 GRAYED：彩色图片变灰
* **参数:** timeline_start
    * **参数类型:** String
    * **描述:** 水印开始时间，与"timeline_duration"配合使用。 取值范围:[0, END)。"END"表示视频结束时间。 单位:秒。
* **参数:** timeline_duration
    * **参数类型:** String
    * **描述:** 水印持续时间，与"timeline_start"配合使用。 取值范围:(0,END-开始时间]。"END"表示视频结束时间。 单位:秒。 默认:END。

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

```bash
GET https://{endpoint}/v1/{project_id}/template/watermark?id={id}
```

## 响应示例

* **状态码:** 200
* **描述:** 处理成功返回。

```json
{
  "templates" : [ {
    "name" : "test",
    "id" : "2305739f855413a84af9e6ad6ebb21be",
    "status" : 0,
    "dx" : "0.05",
    "dy" : "0.05",
    "position" : "TOPRIGHT",
    "width" : "0.12",
    "height" : null,
    "create_time" : "20210204092325",
    "image_url" : "https://103-cn-north-4.cdn-vod.huaweicloud.com/05ab5cef408026f22f62c018de60cf2e/watermark/2305739f855413a84af9e6ad6ebb21be.png",
    "type" : "PNG",
    "watermark_type" : "IMAGE",
    "image_process" : "TRANSPARENT",
    "timeline_start" : null,
    "timeline_duration" : null
  } ],
  "total" : 1
}
```

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 200
    * **描述:** 处理成功返回。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
