# 创建转码模板组集合

## 功能介绍

创建转码模板组集合。

## 接口约束
不同模板的转码产物类型完全一样时，不支持添加至同一个转码模板组中。

相同编码格式的模板，在转码模板组的产物类型，不允许同时出现HLS/DASH（二选一）和HLS_DASH。不同编码格式的模板无影响。

其中codec为视频编码格式，取值为H.264或H.265

## URI

POST /v1/{project_id}/asset/template-collection/transcodings

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

**请求Body参数**
* **参数:** name
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 模板组集合名称
* **参数:** description
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 模板组集合描述
* **参数:** template_group_list
    * **是否必选:** 是
    * **参数类型:** Array of strings
    * **描述:** 模板组列表，模板ID

## 响应参数

### 状态码：201

**响应Body参数**
* **参数:** group_collection_id
    * **参数类型:** String
    * **描述:** 模板组集合ID

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

创建转码模板集合

```bash
POST https://{endpoint}/v1/{project_id}/asset/template-collection/transcodings

Content-Type: application/json
{
  "name": "test",
  "template_group_list": [
    "780640dd1d584a6999b104568c358b78",
    "6a16d8d0161c42caa42b9c148d032871"
  ]
}
```

## 响应示例

* **状态码:** 201
* **描述:** 处理成功返回。

```json
{
  "group_collection_id" : "f9b045e0811c482f9de0d436a5927bb6"
}
```

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 201
    * **描述:** 处理成功返回。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
