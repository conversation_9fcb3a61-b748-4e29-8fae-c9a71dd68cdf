# 查询视频解析任务

## 功能介绍

查询解析任务的状态和结果。

## URI

GET /v1/{project_id}/extract-metadata

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

---

**Query参数**
* **参数:** task_id
    * **是否必选:** 否
    * **参数类型:** Array of strings
    * **描述:** 任务ID。一次最多10个
* **参数:** status
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 任务执行状态。 取值如下： INIT：初始状态 WAITING：等待启动 PREPROCESSING：处理中 SUCCEED：处理成功 FAILED：处理失败 CANCELED：已取消
* **参数:** start_time
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 起始时间。格式为yyyymmddhhmmss。必须是与时区无关的UTC时间，指定task_id时该参数无效。
* **参数:** end_time
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 结束时间。格式为yyyymmddhhmmss。必须是与时区无关的UTC时间，指定task_id时该参数无效。
* **参数:** page
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 分页编号。查询指定“task_id”时，该参数无效。 默认值：0。
* **参数:** size
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 每页记录数。查询指定“task_id”时，该参数无效。 取值范围：[1,100]。 默认值：10。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。
* **参数:** x-language
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 客户端语言

## 响应参数

### 状态码：200

**响应Body参数**
* **参数:** total
    * **参数类型:** Integer
    * **描述:** 任务总数
* **参数:** tasks
    * **参数类型:** Array of ExtractTask objects
    * **描述:** 任务列表

**ExtractTask**
* **参数:** task_id
    * **参数类型:** String
    * **描述:** 任务ID
* **参数:** status
    * **参数类型:** String
    * **描述:** 任务状态。 取值如下： INIT：初始状态。 WAITING：等待启动。 PROCESSING：处理中。 SUCCEED：处理成功。 FAILED：处理失败。 CANCELED：已取消。
* **参数:** create_time
    * **参数类型:** String
    * **描述:** 任务创建时间
* **参数:** start_time
    * **参数类型:** String
    * **描述:** 任务启动时间
* **参数:** end_time
    * **参数类型:** String
    * **描述:** 任务结束时间
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 任务的返回码。
* **参数:** description
    * **参数类型:** String
    * **描述:** 错误描述
* **参数:** user_data
    * **参数类型:** String
    * **描述:** 用户数据。
* **参数:** input
    * **参数类型:** ResourceLocator object
    * **描述:** 源文件信息
* **参数:** output
    * **参数类型:** ResourceLocator object
    * **描述:** 输出文件信息
* **参数:** metadata
    * **参数类型:** MetaData object
    * **描述:** 输出参数

**ResourceLocator**

`ResourceLocator` 是一个统一的、多态的资源描述对象，用于精确指定文件在任何存储系统中的位置。

* **参数:** type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源定位符的类型。它决定了 `config` 对象的结构。可选值：LOCAL_FILE（本地文件系统）、MEDIA_ASSET（内部媒体资源ID）、OBJECT_STORAGE（云存储）、URL（外部网络地址）。
* **参数:** config
    * **是否必选:** 是
    * **参数类型:** Object
    * **描述:** 定位符的具体配置信息，其结构由 `type` 的值决定。

### **config 结构详情**

#### 当 type = LOCAL_FILE
* **参数:** path
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 文件系统路径。用于 input 时：必须是单个文件的绝对路径。用于 output 时：必须是目录的绝对路径，转码结果将存入此目录。

#### 当 type = MEDIA_ASSET
* **参数:** mediaAssetId
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 在媒体资源库中注册的唯一ID。

#### 当 type = OBJECT_STORAGE
* **参数:** storageName
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 您在"对象存储配置接口"中创建的、用于标识特定存储账户的唯一自定义别名。例如: "r2-main-storage", "s3-main-storage"。系统会根据此名称查找对应的提供商类型（R2/S3）、访问凭证和终端节点（Endpoint）等信息。
* **参数:** bucket
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** Bucket（桶）的名称。
* **参数:** key
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 对象的键（Key）。用于 input 时：必须是单个对象的完整Key。用于 output 时：必须是目录前缀。
* **参数:** location
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 存储桶所在的地理区域。对于 Cloudflare R2，此项为可选，可省略或推荐使用 auto。对于 AWS S3 等强区域性服务，此项为必需，例如 us-east-1。

#### 当 type = URL
* **参数:** url
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 资源的完整URL地址。
* **参数:** headers
    * **是否必选:** 否
    * **参数类型:** Object
    * **描述:** 访问该URL时需要附加的HTTP请求头。可用于身份验证等场景。这是一个键值对对象，例如 {"Authorization": "Bearer your-token"}。

    
**MetaData**
* **参数:** size
    * **参数类型:** Long
    * **描述:** 文件大小。 单位：byte。
* **参数:** duration_ms
    * **参数类型:** Double
    * **描述:** 视频时长，带小数位显示。 单位：秒。
* **参数:** duration
    * **参数类型:** Long
    * **描述:** 视频时长。 单位：秒。
* **参数:** format
    * **参数类型:** String
    * **描述:** 文件封装格式。
* **参数:** md5
    * **参数类型:** String
    * **描述:** 视频的 md5 值。
* **参数:** bitrate
    * **参数类型:** Long
    * **描述:** 总码率。 单位：bit/秒
* **参数:** video
    * **参数类型:** Array of VideoInfo objects
    * **描述:** 视频流元数据。
* **参数:** audio
    * **参数类型:** Array of AudioInfo objects
    * **描述:** 音频流元数据。

**VideoInfo**
* **参数:** width
    * **参数类型:** Integer
    * **描述:** 视频宽度
* **参数:** height
    * **参数类型:** Integer
    * **描述:** 视频高度
* **参数:** bitrate
    * **参数类型:** Integer
    * **描述:** 视频码率。 单位: kbit/s 说明： 视频解析接口2024年8月7日做了如下变更： 变更前：视频解析任务，如果为同步解析，即sync参数配置为1时，解析响应VideoInfo中的参数bitrate单位实际为bit/s。 变更后：视频解析任务同步解析响应VideoInfo中的参数bitrate单位变更为kbit/s。 上述更新，可能会影响历史用户的转码业务判断，请关注并及时调整业务。
* **参数:** bitrate_bps
    * **参数类型:** Long
    * **描述:** 视频码率。 单位: bit/s
* **参数:** frame_rate
    * **参数类型:** Integer
    * **描述:** 帧率。 取值范围：0或[5,60]，0表示自适应。 单位：帧每秒。 说明： 若设置的帧率不在取值范围内，则自动调整为0，若设置的帧率高于片源帧率，则自动调整为片源帧率。
* **参数:** codec
    * **参数类型:** String
    * **描述:** 视频编码格式
* **参数:** duration
    * **参数类型:** String
    * **描述:** 视频流时长，单位：秒
* **参数:** duration_ms
    * **参数类型:** String
    * **描述:** 视频流时长，单位：毫秒
* **参数:** rotate
    * **参数类型:** Float
    * **描述:** 视频拍摄时的选择角度，单位：度。

**AudioInfo**
* **参数:** codec
    * **参数类型:** String
    * **描述:** 音频编码格式
* **参数:** sample
    * **参数类型:** Integer
    * **描述:** 音频采样率
* **参数:** channels
    * **参数类型:** Integer
    * **描述:** 音频信道
* **参数:** bitrate
    * **参数类型:** Integer
    * **描述:** 音频码率。 单位: kbit/s
* **参数:** bitrate_bps
    * **参数类型:** Long
    * **描述:** 音频码率。 单位: bit/s
* **参数:** duration
    * **参数类型:** String
    * **描述:** 音频流时长，单位：秒
* **参数:** duration_ms
    * **参数类型:** String
    * **描述:** 音频流时长，单位：毫秒

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 状态码

****
* **状态码:** 200
    * **描述:** 查询视频解析任务成功。
* **状态码:** 400
    * **描述:** 查询视频解析任务失败。

## 错误码

请参见错误码。
