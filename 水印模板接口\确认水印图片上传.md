# 确认水印图片上传

## 功能介绍

确认水印图片上传状态。

## URI

POST /v1/{project_id}/watermark/status/uploaded

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

**请求Body参数**
* **参数:** id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 水印配置模板id。
* **参数:** status
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 水印上传状态，传值支持"SUCCEED"和"FAILED"。

## 响应参数

### 状态码：200

**响应Body参数**
* **参数:** id
    * **参数类型:** String
    * **描述:** 水印配置模板id
* **参数:** image_url
    * **参数类型:** String
    * **描述:** 水印图片的下载url

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

确认水印图片上传状态

```bash
POST https://{endpoint}/v1/{project_id}/watermark/status/uploaded

Content-Type: application/json
{
  "id": "2305739f855413a84af9e6ad6e2b21be",
  "status": "SUCCEED"
}
```

## 响应示例

* **状态码:** 200
* **描述:** 处理成功返回。

```markdown
{
  "id" : "2305739f855413a84af9e6ad6e2b21be",
  "image_url" : "https://vod-bucket-26-cn-north-4.obs.cn-north-4.myhuaweicloud.com:443/05ab5cef408026f22f62c018de60cf2e/a52ba84366abebb4c4614e1b16973549/watermark.png?AWSAccessKeyId=CBN2J**********0RCSN&Expires=1625479312&Signature=kZYh0hEos2V**********AHGyXA%3D"
}
```

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 200
    * **描述:** 处理成功返回。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
