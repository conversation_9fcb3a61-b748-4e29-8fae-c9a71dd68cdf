# 修改转码模板

## 功能介绍

修改转码模板。

## URI

PUT /v1/{project_id}/asset/template/transcodings

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

**请求Body参数**
* **参数:** group_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 模板组ID
* **参数:** name
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 模板组名称
* **参数:** is_default
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 是否是默认转码模板，默认false不设置为默认
* **参数:** is_auto_encrypt
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 配置是否加密输出音视频，默认false不加密。 当前只支持加密输出HLS格式的音视频流，不会对原始上传的音视频进行加密处理。非HLS转码输出的音视频格式，此字段不可配置。开启此配置前，请参考《VOD用户指南》的“HLS加密设置”章节获取密钥URL。
* **参数:** quality_info_list
    * **是否必选:** 否
    * **参数类型:** Array of QualityInfoList objects
    * **描述:** 画质配置信息列表，如果不携带或者携带为空，则不更新模板中该部分数据。
* **参数:** watermark_template_ids
    * **是否必选:** 否
    * **参数类型:** Array of strings
    * **描述:** 绑定的水印模板组ID数组
* **参数:** description
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 模板介绍
* **参数:** common
    * **是否必选:** 否
    * **参数类型:** CommonInfo object
    * **描述:** 高清低码开关，编码格式多路共同参数

---

**QualityInfoList**
* **参数:** video
    * **是否必选:** 否
    * **参数类型:** VideoInfo object
    * **描述:** 模板视频信息。video和audio至少配置一个参数。
* **参数:** audio
    * **是否必选:** 否
    * **参数类型:** AudioInfo object
    * **描述:** 模板音频信息

---

**VideoInfo**
* **参数:** quality
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 画质 4K默认分辨率3840*2160，码率8000kbit/s 2K默认分辨率2560*1440，码率7000kbit/s FULL_HD默认分辨率1920*1080，码率3000kbit/s HD默认分辨率1280*720，码率1000kbit/s SD默认分辨率854*480，码率600kbit/s FLUENT默认分辨率480*270，码率300kbit/s
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频宽度 H264编码：0或[32,4096]之间2的整数倍 H265编码：0或[160,4096]之间2的整数倍
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 视频高度 H264编码：0或[32,2880]之间2的整数倍 H265编码：0或[160,2880]之间2的整数倍
* **参数:** bitrate
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 平均码率，单位：kbit/s。 参考取值范围：0或[40,30000]之间的整数。
* **参数:** frame_rate
    * **是否必选:** 是
    * **参数类型:** Integer
    * **描述:** 帧率，单位是帧每秒。 取值范围：[0-75]之间的整数，小于5帧或大于60帧代表自适应。

---

**AudioInfo**
* **参数:** sample_rate
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 音频采样率（有效值范围） AUDIO_SAMPLE_AUTO (default), AUDIO_SAMPLE_22050：22050Hz AUDIO_SAMPLE_32000：32000Hz AUDIO_SAMPLE_44100：44100Hz AUDIO_SAMPLE_48000：48000Hz AUDIO_SAMPLE_96000：96000Hz
* **参数:** bitrate
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 音频码率（单位：Kbps）
* **参数:** channels
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 声道数（有效值范围） AUDIO_CHANNELS_1:单声道 AUDIO_CHANNELS_2：双声道 (default) AUDIO_CHANNELS_5_1：5.1声道

---

**CommonInfo**
* **参数:** pvc
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 高清低码开关
* **参数:** video_codec
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 视频编码格式 H264 H265
* **参数:** audio_codec
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 音频编码格式 AAC：AAC格式 (default) HEAAC1：HEAAC1格式 HEAAC2：HEAAC2格式 MP3：MP3格式
* **参数:** is_black_cut
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 黑边剪裁类型，默认false不开启黑边剪裁
* **参数:** format
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 转码格式 MP4 HLS DASH DASH_HLS MP3 ADTS UNKNOW
* **参数:** hls_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 分片时长，范围2-10（默认为5秒）
* **参数:** upsample
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 上采样开关，开启后可以提升视频的分辨率，增加采样点的数量，默认值:false，表示不开启上采样。
* **参数:** adaptation
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 转码后的片源分辨率自适应方式。 取值如下： SHORT：短边自适应。转码模板的宽和高都设置，用其中的短边与片源短边对比，然后片源长边按此比例进行缩放。 LONG：长边自适应。转码模板的宽和高都设置，用其中的长边与片源长边对比，然后片源短边按此比例进行缩放。 NONE：按设置宽高适应，为默认值。按照转码模板设置的宽高输出。
* **参数:** preset
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** 编码质量等级，取值[0,2] 0表示默认方式，1表示转码效率优先，2表示转码质量优先。
* **参数:** max_iframes_interval
    * **是否必选:** 否
    * **参数类型:** Integer
    * **描述:** I帧最大间隔，取值范围：[2，10]。默认值：5，单位秒。
* **参数:** hls_audio_separate
    * **是否必选:** 否
    * **参数类型:** Boolean
    * **描述:** 转码后音频是否独立存储。
* **参数:** hls_segment_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** HLS分片的封装格式，目前支持TS和FMP4，默认TS格式

## 响应参数

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

修改转码模板

```css
PUT https://{endpoint}/v1/{project_id}/asset/template/transcodings

Content-Type: application/json
{
  "group_id": "f9b045e0811c482f9de0d436a5927bb6",
  "name": "trans_template_test",
  "is_default": true,
  "quality_info_list": [
    {
      "video": {
        "width": 1280,
        "height": 720,
        "bitrate": 1000,
        "quality": "HD",
        "frame_rate": 0
      },
      "audio": {
        "sample_rate": "AUDIO_SAMPLE_AUTO",
        "channels": "AUDIO_CHANNELS_1",
        "bitrate": 0
      }
    }
  ],
  "watermark_template_ids": [],
  "common": {
    "pvc": false,
    "video_codec": "H264",
    "audio_codec": "AAC",
    "format": "HLS",
    "hls_interval": 5
  }
}
```

## 响应示例

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 200
    * **描述:** 处理成功返回。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
