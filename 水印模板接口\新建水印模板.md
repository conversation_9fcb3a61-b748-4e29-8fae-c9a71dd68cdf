# 创建水印模板

## 功能介绍

创建水印模板。

## URI

POST /v1/{project_id}/template/watermark

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

---

**请求Body参数**
* **参数:** name
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 水印模板名称。
* **参数:** watermark_type
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印类型，当前只支持IMAGE（图片水印）。
* **参数:** image_process
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** watermark_type设置为IMAGE时有效。 目前包括： ORIGINAL：只做简单缩放，不做其他处理 TRANSPARENT：图片底色透明 GRAYED：彩色图片变灰
* **参数:** dx
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片相对输出视频的水平偏移量，默认值是0。 设置方法有两种，建议小数型，整数型可能导致控制台图片无法预览，但不影响转码： 整数型：表示图片起点水平偏移视频顶点的像素值，单位px。取值范围：[0，4096] 小数型：表示图片起点相对于视频分辨率宽的水平偏移比率。取值范围：(0，1)，支持4位小数，如0.9999，超出部分系统自动丢弃。
* **参数:** dy
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片相对输出视频的垂直偏移量，默认值是0。 设置方法有两种，建议小数型，整数型可能导致控制台图片无法预览，但不影响转码： 整数型：表示图片起点垂直偏移视频顶点的像素值，单位px。取值范围：[0，4096] 小数型：表示图片起点相对于视频分辨率高的垂直偏移比率。取值范围：(0，1)，支持4位小数，如0.9999，超出部分系统自动丢弃。
* **参数:** position
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印的位置，默认TOPRIGHT。 目前包括： TOPRIGHT：右上位置 TOPLEFT：左上位置 BOTTOMRIGHT：右下位置 BOTTOMLEFT：左下位置
* **参数:** width
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片宽，建议小数型，整数型可能导致控制台图片无法预览，但不影响转码。 值有两种形式： 整数型代水印图片宽的像素值，范围[8，4096]，单位px。 小数型代表相对输出视频分辨率宽的比率，范围(0,1)，支持4位小数，如0.9999，超出部分系统自动丢弃。
* **参数:** height
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片高，建议小数型，整数型可能导致控制台图片无法预览，但不影响转码。 值有两种形式： 整数型代表水印图片高的像素值，范围[8，4096]，单位px。 小数型代表相对输出视频分辨率高的比率，范围(0，1)，支持4位小数，如0.9999，超出部分系统自动丢弃。
* **参数:** timeline_start
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印开始时间，与"timeline_duration"配合使用。 取值范围:[0, END)。"END"表示视频结束时间。 单位:秒。
* **参数:** timeline_duration
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印持续时间，与"timeline_start"配合使用。 取值范围:(0,END-开始时间]。"END"表示视频结束时间。 单位:秒。 默认:END。
* **参数:** type
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 仅支持png、jpg和jpeg格式的图片作为水印模板，传值仅支持JPG、JPEG和PNG三种字符串。
* **参数:** md5
    * **是否必选:** 否
    * **参数类型:** String
    * **描述:** 水印图片MD5值。

## 响应参数

### 状态码：200

**响应Body参数**
* **参数:** id
    * **参数类型:** String
    * **描述:** 水印配置模板id
* **参数:** upload_url
    * **参数类型:** String
    * **描述:** 水印图片上传地址

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

创建水印模板

```bash
POST https://{endpoint}/v1/{project_id}/template/watermark

Content-Type: application/json
{
  "name": "test",
  "type": "PNG"
}
```

## 响应示例

* **状态码:** 200
* **描述:** 处理成功返回

```markdown
{
  "id" : "a52ba84366abebb4c4614e1b16973549",
  "upload_url" : "https://vod-bucket-26-cn-north-4.obs.cn-north-4.myhuaweicloud.com:443/05ab5cef408026f22f62c018de60cf2e/a52ba84366abebb4c4614e1b16973549/watermark.png?AWSAccessKeyId=CBN2J**********0RCSN&Expires=1625479312&Signature=kZYh0hEos2V**********AHGyXA%3D"
}
```

* **状态码:** 400
* **描述:** 处理失败返回

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 200
    * **描述:** 处理成功返回
* **状态码:** 400
    * **描述:** 处理失败返回

## 错误码

请参见错误码。
