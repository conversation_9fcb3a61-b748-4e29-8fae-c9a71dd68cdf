# 删除自定义模板

## 功能介绍

删除自定义模板。

## URI

DELETE /v1/{project_id}/asset/template/transcodings

**路径参数**
* **参数:** project_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 项目ID。

---

**Query参数**
* **参数:** group_id
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 模板id

## 请求参数

**请求Header参数**
* **参数:** Authorization
    * **是否必选:** 是
    * **参数类型:** String
    * **描述:** 携带的鉴权信息。

## 响应参数

### 状态码：400

**响应Body参数**
* **参数:** error_code
    * **参数类型:** String
    * **描述:** 错误码。
* **参数:** error_msg
    * **参数类型:** String
    * **描述:** 错误描述。

## 请求示例

删除转码模板

```bash
DELETE https://{endpoint}/v1/{project_id}/asset/template/transcodings?group_id={group_id}
```

## 响应示例

* **状态码:** 400
* **描述:** 处理失败返回。

```json
{
  "error_code" : "VOD.10053",
  "error_msg" : "The request parameter is illegal, illegal field: {xx}."
}
```
## 状态码

* **状态码:** 204
    * **描述:** 处理成功返回204 No Content。
* **状态码:** 400
    * **描述:** 处理失败返回。

## 错误码

请参见错误码。
